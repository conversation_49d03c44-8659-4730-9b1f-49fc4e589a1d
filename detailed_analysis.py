#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细的重复内容分析器
手动分析合并后文件中的重复内容
"""

import re
from datetime import datetime

def analyze_file():
    """手动分析文件内容"""
    
    # 读取文件
    with open("outcome/合并后的文件.txt", 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    lines = [line.rstrip('\n\r') for line in lines]
    
    print("开始详细分析...")
    
    # 分析结果
    results = []
    
    # 1. 明显的重复开头
    results.append("## 1. 完全重复的内容")
    results.append("")
    results.append("### 信件开头重复")
    results.append("- **第1行和第59行**: 都是 '亲爱的点点：'")
    results.append("- **分析**: 这表明文件中包含了两封独立的信件，都以相同的称呼开始")
    results.append("")
    
    # 2. 结尾重复
    results.append("### 信件结尾重复")
    results.append("- **第33-34行和第91-92行**: 都是 '一直想你的，\\n文俊'")
    results.append("- **分析**: 两封信都以相同的署名结束")
    results.append("")
    
    # 3. 查找内容重复
    results.append("## 2. 内容主题重复")
    results.append("")
    
    # 分析具体的重复主题
    themes = {
        "道歉和解释": [],
        "表达想念": [],
        "回忆相识": [],
        "自我反思": [],
        "承诺和未来": []
    }
    
    # 手动标记一些重复的主题段落
    results.append("### 关于'大木头'和性格的描述")
    results.append("- **第5行**: '你总叫我\"大木头\"，甚至在信里调侃我是不是\"小汤姆\"'")
    results.append("- **第51行**: '你说我像个\"大木头\"，其实很贴切'")
    results.append("- **分析**: 两处都在讨论'大木头'这个称呼，内容有重复")
    results.append("")
    
    results.append("### 关于童话故事的表述")
    results.append("- **第7行**: '我不是一个听着童话故事长大的孩子'")
    results.append("- **第69行**: '我曾对你说，我不是一个\"听着童话故事入睡的人\"'")
    results.append("- **分析**: 两处都在表达同样的意思，用词略有不同但含义相同")
    results.append("")
    
    results.append("### 关于'小镇做题家'的自我描述")
    results.append("- **第5行**: '一个不善于将情感直接宣之于口的\"小镇做题家\"'")
    results.append("- **第39行**: '我这个\"小镇做题家\"'")
    results.append("- **第69行**: '一种\"小镇做题家\"式的自卑与不安'")
    results.append("- **分析**: 多次使用相同的自我标签，有重复感")
    results.append("")
    
    results.append("### 关于相识初期的描述")
    results.append("- **第3行**: '你明亮地敲出一句\"你好呀\"，而我，笨拙地，只能用三个句号来掩饰内心的不知所措'")
    results.append("- **第37行**: '我们的相遇，是从你的\"你好呀\"和一个句号开始的'")
    results.append("- **第65行**: '你像一颗明亮的彗星，带着\"你好呀\"的璀璨光芒闯入我安静的轨道，而我，是那颗只会用沉默的\"。。。\"来回应的、迟钝的行星'")
    results.append("- **分析**: 三处都在描述初次相遇的场景，表达方式不同但内容重复")
    results.append("")
    
    results.append("### 关于那句伤人话的解释")
    results.append("- **第13-21行**: 详细解释了说'不要找我了，谢谢'的原因")
    results.append("- **第71-75行**: 再次解释同一件事，用了不同的表达方式")
    results.append("- **分析**: 两段都在为同一句话道歉和解释，内容高度重复")
    results.append("")
    
    results.append("### 关于'想你'的表达")
    results.append("- **第29行**: '\"想你\"这件事，对我来说，从来都不是一个需要被触发的、一次性的动作'")
    results.append("- **第58行**: '我的\"想你\"，早已在每一次你没有回复的间隙里开始了'")
    results.append("- **分析**: 两处都在表达'一直在想你'这个意思，表述方式相似")
    results.append("")
    
    # 3. 语义重复分析
    results.append("## 3. 语义重复分析")
    results.append("")
    results.append("### 表达方式的重复模式")
    results.append("1. **多次使用相同的比喻**: 如'大木头'、'小镇做题家'等标签")
    results.append("2. **重复的情感表达**: 多次表达道歉、想念、自我反思")
    results.append("3. **相似的句式结构**: 多处使用'我习惯了...却忘了...'的句式")
    results.append("4. **重复的场景描述**: 多次描述初次相遇的场景")
    results.append("")
    
    # 4. 结构分析
    results.append("## 4. 文件结构分析")
    results.append("")
    results.append("根据分析，这个文件包含了两封完整的信件：")
    results.append("- **第一封信**: 第1-34行")
    results.append("- **第二封信**: 第35-58行（较短）")
    results.append("- **第三封信**: 第59-92行")
    results.append("")
    results.append("三封信在主题和内容上有大量重复，可能是：")
    results.append("1. 同一封信的不同版本")
    results.append("2. 作者多次修改后的不同草稿")
    results.append("3. 表达相同情感的多次尝试")
    results.append("")
    
    # 5. 建议
    results.append("## 5. 修改建议")
    results.append("")
    results.append("### 立即需要处理的重复")
    results.append("1. **删除重复的开头和结尾** (第59行和第91-92行)")
    results.append("2. **合并相似的段落**，避免同一主题的重复表达")
    results.append("3. **统一比喻和标签的使用**，避免过度重复")
    results.append("")
    results.append("### 内容整合建议")
    results.append("1. **选择最佳表达方式**：对于相同主题的多种表达，选择最感人或最准确的版本")
    results.append("2. **重新组织结构**：将分散的相同主题内容整合到一起")
    results.append("3. **增加内容多样性**：用不同的词汇和句式表达相似的情感")
    results.append("")
    results.append("### 保留建议")
    results.append("考虑到这是一封情书，某些重复可能是为了强调情感，建议：")
    results.append("1. **保留情感强调**：如'一直想你'这样的表达可以保留")
    results.append("2. **删除技术性重复**：如完全相同的开头、结尾等")
    results.append("3. **优化表达方式**：将重复的内容用更丰富的语言重新表达")
    
    # 写入报告
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    final_report = [
        "# 重复内容详细分析报告",
        f"## 文件: outcome/合并后的文件.txt",
        f"## 分析时间: {timestamp}",
        f"## 总行数: {len(lines)}",
        "",
        "## 分析概述",
        "经过详细分析，发现该文件包含三封内容高度重复的信件，存在以下主要问题：",
        "- 完全相同的开头和结尾",
        "- 重复的主题表达",
        "- 相似的情感描述",
        "- 重复的场景回忆",
        "",
    ] + results
    
    with open("详细重复分析报告.md", 'w', encoding='utf-8') as f:
        f.write('\n'.join(final_report))
    
    print("详细分析报告已生成: 详细重复分析报告.md")

if __name__ == "__main__":
    analyze_file()
