#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精确的行级重复分析
逐行分析合并后文件中的重复内容
"""

import re
from datetime import datetime

def precise_analysis():
    """精确分析每一行的重复情况"""
    
    # 读取文件
    with open("outcome/合并后的文件.txt", 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    lines = [line.rstrip('\n\r') for line in lines]
    
    print("开始精确行级分析...")
    
    # 分析结果
    results = []
    
    # 添加标题
    results.append("# 精确重复内容分析报告")
    results.append(f"## 文件: outcome/合并后的文件.txt")
    results.append(f"## 分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    results.append(f"## 总行数: {len(lines)}")
    results.append("")
    
    # 1. 完全相同的行
    results.append("## 1. 完全相同的行（逐字重复）")
    results.append("")
    
    exact_matches = []
    
    # 检查每一行是否有完全相同的
    for i, line1 in enumerate(lines):
        if not line1.strip():  # 跳过空行
            continue
        for j, line2 in enumerate(lines[i+1:], i+1):
            if line1.strip() == line2.strip():
                exact_matches.append((i+1, j+1, line1.strip()))
    
    for i, (line1, line2, content) in enumerate(exact_matches, 1):
        results.append(f"### 重复组 {i}")
        results.append(f"**行号**: 第{line1}行 和 第{line2}行")
        results.append(f"**内容**: {content}")
        results.append("")
    
    # 2. 包含相同关键短语的行
    results.append("## 2. 包含相同关键短语的行")
    results.append("")
    
    # 定义关键短语
    key_phrases = [
        "亲爱的点点",
        "大木头",
        "小镇做题家", 
        "童话故事",
        "你好呀",
        "不要找我了",
        "想你",
        "一直想你的",
        "文俊",
        "对不起",
        "我习惯了",
        "我忘了",
        "我的世界",
        "你的世界"
    ]
    
    for phrase in key_phrases:
        matching_lines = []
        for i, line in enumerate(lines, 1):
            if phrase in line:
                matching_lines.append((i, line.strip()))
        
        if len(matching_lines) > 1:
            results.append(f"### 包含'{phrase}'的行")
            for line_num, content in matching_lines:
                results.append(f"- **第{line_num}行**: {content}")
            results.append("")
    
    # 3. 语义相似的句子分析
    results.append("## 3. 语义相似的句子（详细对比）")
    results.append("")
    
    # 手动标记的语义相似组
    semantic_groups = [
        {
            "theme": "初次相遇的描述",
            "lines": [
                (3, "收到你的信，是在一个寻常的深夜...你明亮地敲出一句'你好呀'，而我，笨拙地，只能用三个句号来掩饰内心的不知所措"),
                (37, "我们的相遇，是从你的'你好呀'和一个句号开始的"),
                (65, "你像一颗明亮的彗星，带着'你好呀'的璀璨光芒闯入我安静的轨道")
            ]
        },
        {
            "theme": "关于'大木头'的描述",
            "lines": [
                (5, "你总叫我'大木头'，甚至在信里调侃我是不是'小汤姆'"),
                (51, "你说我像个'大木头'，其实很贴切。木头不会说甜言蜜语，但会努力扎根")
            ]
        },
        {
            "theme": "关于童话故事的表述",
            "lines": [
                (7, "我不是一个听着童话故事长大的孩子"),
                (69, "我曾对你说，我不是一个'听着童话故事入睡的人'")
            ]
        },
        {
            "theme": "关于伤人话的道歉",
            "lines": [
                (13, "信里你写道：'你老早说咱两根本没必要浪费时间'，以及那句冰冷的'不要找我了，谢谢'"),
                (72, "读到你说'我真的很累不要找你了谢谢'，我的心像是被那晚窗外的雷声劈中")
            ]
        }
    ]
    
    for group in semantic_groups:
        results.append(f"### {group['theme']}")
        for line_num, content in group['lines']:
            results.append(f"- **第{line_num}行**: {content}")
        results.append("")
    
    # 4. 重复的情感表达模式
    results.append("## 4. 重复的情感表达模式")
    results.append("")
    
    patterns = [
        {
            "pattern": "道歉表达",
            "examples": [
                (21, "对不起，点点。这句道歉来得太迟，但我必须说。"),
                (75, "对不起，点点。这句话迟来了很久，但它发自我内心最深处。")
            ]
        },
        {
            "pattern": "自我反思句式",
            "examples": [
                (5, "我习惯于用逻辑和分析去构建我的世界，去理解那些复杂的代码和物理定律。"),
                (39, "我习惯了分析、解决，习惯了用逻辑去构建一切，包括关心。")
            ]
        },
        {
            "pattern": "表达想念",
            "examples": [
                (29, "'想你'这件事，对我来说，从来都不是一个需要被触发的、一次性的动作。"),
                (58, "我的'想你'，早已在每一次你没有回复的间隙里开始了。")
            ]
        }
    ]
    
    for pattern in patterns:
        results.append(f"### {pattern['pattern']}")
        for line_num, content in pattern['examples']:
            results.append(f"- **第{line_num}行**: {content}")
        results.append("")
    
    # 5. 结构重复分析
    results.append("## 5. 文件结构重复分析")
    results.append("")
    results.append("### 信件结构对比")
    results.append("")
    results.append("**第一封信 (第1-34行)**:")
    results.append("- 开头: 亲爱的点点：")
    results.append("- 结尾: 一直想你的，文俊")
    results.append("")
    results.append("**第二封信 (第35-58行)**:")
    results.append("- 开头: 提笔写这封信前...")
    results.append("- 结尾: 我的想念，一直都在...")
    results.append("")
    results.append("**第三封信 (第59-92行)**:")
    results.append("- 开头: 亲爱的点点：（与第一封完全相同）")
    results.append("- 结尾: 一直想你的，文俊（与第一封完全相同）")
    results.append("")
    
    # 6. 修改建议
    results.append("## 6. 具体修改建议")
    results.append("")
    results.append("### 必须删除的重复内容")
    results.append("1. **第59行**: 删除重复的'亲爱的点点：'")
    results.append("2. **第91-92行**: 删除重复的结尾'一直想你的，文俊'")
    results.append("")
    results.append("### 需要合并的相似内容")
    results.append("1. **第3行和第65行**: 关于初次相遇的描述，选择更好的表达保留一个")
    results.append("2. **第13-21行和第71-75行**: 关于伤人话的解释，合并为一段")
    results.append("3. **第5行和第51行**: 关于'大木头'的描述，整合表达")
    results.append("4. **第7行和第69行**: 关于童话故事的表述，统一表达")
    results.append("")
    results.append("### 需要重新表达的重复主题")
    results.append("1. **'小镇做题家'标签**: 在第5、39、69行多次出现，建议减少使用频率")
    results.append("2. **想念的表达**: 第29行和第58行表达相似，可以合并或用不同方式表达")
    results.append("3. **道歉的表达**: 第21行和第75行几乎相同，建议保留一个")
    results.append("")
    
    # 写入报告
    with open("精确重复分析报告.md", 'w', encoding='utf-8') as f:
        f.write('\n'.join(results))
    
    print("精确分析报告已生成: 精确重复分析报告.md")
    print(f"发现 {len(exact_matches)} 组完全重复的行")

if __name__ == "__main__":
    precise_analysis()
