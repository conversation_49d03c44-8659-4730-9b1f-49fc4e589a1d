#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重复内容检测器
检测合并后文件中的内容重复和语义重复部分
"""

import re
import difflib
from collections import defaultdict
from datetime import datetime

def read_file(file_path):
    """读取文件内容"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        return [line.rstrip('\n\r') for line in lines]
    except Exception as e:
        print(f"读取文件失败: {e}")
        return []

def clean_text(text):
    """清理文本，去除标点符号和空格，用于比较"""
    # 去除标点符号和空格
    cleaned = re.sub(r'[^\w\u4e00-\u9fff]', '', text)
    return cleaned.lower()

def find_exact_duplicates(lines):
    """查找完全相同的行"""
    duplicates = []
    line_dict = defaultdict(list)
    
    for i, line in enumerate(lines, 1):
        if line.strip():  # 忽略空行
            line_dict[line.strip()].append(i)
    
    for line_content, line_numbers in line_dict.items():
        if len(line_numbers) > 1:
            duplicates.append({
                'type': '完全重复',
                'content': line_content,
                'lines': line_numbers
            })
    
    return duplicates

def find_similar_lines(lines, similarity_threshold=0.6):
    """查找相似的行（语义重复）"""
    similar_groups = []
    processed = set()

    for i, line1 in enumerate(lines):
        if i in processed or not line1.strip() or len(line1.strip()) < 10:
            continue

        current_group = [(i+1, line1.strip())]
        max_similarity = 0

        for j, line2 in enumerate(lines[i+1:], i+1):
            if j in processed or not line2.strip() or len(line2.strip()) < 10:
                continue

            # 计算相似度
            similarity = difflib.SequenceMatcher(None, clean_text(line1), clean_text(line2)).ratio()

            if similarity >= similarity_threshold:
                current_group.append((j+1, line2.strip()))
                processed.add(j)
                max_similarity = max(max_similarity, similarity)

        if len(current_group) > 1:
            processed.add(i)
            similar_groups.append({
                'type': '语义重复',
                'similarity': max_similarity,
                'lines': current_group
            })

    return similar_groups

def find_paragraph_duplicates(lines):
    """查找段落级别的重复"""
    paragraphs = []
    current_paragraph = []
    current_start_line = 1
    
    # 将文本分割成段落
    for i, line in enumerate(lines, 1):
        if line.strip():
            if not current_paragraph:
                current_start_line = i
            current_paragraph.append(line.strip())
        else:
            if current_paragraph:
                paragraphs.append({
                    'content': '\n'.join(current_paragraph),
                    'start_line': current_start_line,
                    'end_line': i-1,
                    'lines': list(range(current_start_line, i))
                })
                current_paragraph = []
    
    # 处理最后一个段落
    if current_paragraph:
        paragraphs.append({
            'content': '\n'.join(current_paragraph),
            'start_line': current_start_line,
            'end_line': len(lines),
            'lines': list(range(current_start_line, len(lines)+1))
        })
    
    # 查找重复段落
    duplicates = []
    processed = set()
    
    for i, para1 in enumerate(paragraphs):
        if i in processed:
            continue
            
        similar_paras = [para1]
        processed.add(i)
        
        for j, para2 in enumerate(paragraphs[i+1:], i+1):
            if j in processed:
                continue
                
            # 计算段落相似度
            similarity = difflib.SequenceMatcher(None, clean_text(para1['content']), clean_text(para2['content'])).ratio()
            
            if similarity >= 0.7:  # 段落相似度阈值
                similar_paras.append(para2)
                processed.add(j)
        
        if len(similar_paras) > 1:
            duplicates.append({
                'type': '段落重复',
                'paragraphs': similar_paras,
                'similarity': similarity
            })
    
    return duplicates

def find_phrase_duplicates(lines, min_length=8):
    """查找重复的短语或句子片段"""
    duplicates = []

    # 收集所有有意义的短语（长度大于min_length的文本片段）
    phrases = defaultdict(list)

    for line_num, line in enumerate(lines, 1):
        line_text = line.strip()
        if not line_text:
            continue

        # 按标点符号分割句子
        sentences = re.split(r'[，。！？；：、]', line_text)

        for sentence in sentences:
            sentence = sentence.strip()
            if len(sentence) >= min_length:
                # 清理后的文本用于比较
                cleaned = clean_text(sentence)
                if len(cleaned) >= min_length:
                    phrases[cleaned].append({
                        'line': line_num,
                        'original': sentence,
                        'context': line_text
                    })

    # 查找重复的短语
    for cleaned_phrase, occurrences in phrases.items():
        if len(occurrences) > 1:
            duplicates.append({
                'type': '短语重复',
                'content': occurrences[0]['original'],
                'occurrences': occurrences,
                'count': len(occurrences)
            })

    return duplicates

def find_semantic_duplicates(lines):
    """查找语义重复的内容（更深层的分析）"""
    duplicates = []

    # 提取关键句子进行比较
    key_sentences = []
    for line_num, line in enumerate(lines, 1):
        line_text = line.strip()
        if len(line_text) > 15:  # 只分析较长的句子
            key_sentences.append({
                'line': line_num,
                'text': line_text,
                'cleaned': clean_text(line_text)
            })

    # 比较句子相似度
    processed = set()
    for i, sent1 in enumerate(key_sentences):
        if i in processed:
            continue

        similar_group = [sent1]

        for j, sent2 in enumerate(key_sentences[i+1:], i+1):
            if j in processed:
                continue

            # 计算语义相似度
            similarity = difflib.SequenceMatcher(None, sent1['cleaned'], sent2['cleaned']).ratio()

            # 检查是否包含相同的关键词组
            words1 = set(re.findall(r'[\u4e00-\u9fff]+', sent1['text']))
            words2 = set(re.findall(r'[\u4e00-\u9fff]+', sent2['text']))

            if len(words1) > 0 and len(words2) > 0:
                word_similarity = len(words1 & words2) / len(words1 | words2)
            else:
                word_similarity = 0

            # 综合相似度判断
            if similarity > 0.6 or word_similarity > 0.7:
                similar_group.append(sent2)
                processed.add(j)

        if len(similar_group) > 1:
            processed.add(i)
            duplicates.append({
                'type': '语义重复内容',
                'sentences': similar_group,
                'similarity': max([difflib.SequenceMatcher(None, similar_group[0]['cleaned'], s['cleaned']).ratio() for s in similar_group[1:]])
            })

    return duplicates

def generate_report(file_path, output_path):
    """生成重复内容报告"""
    lines = read_file(file_path)
    if not lines:
        return

    print("正在分析文件内容...")

    # 查找各种类型的重复
    exact_duplicates = find_exact_duplicates(lines)
    similar_lines = find_similar_lines(lines)
    paragraph_duplicates = find_paragraph_duplicates(lines)
    phrase_duplicates = find_phrase_duplicates(lines)
    semantic_duplicates = find_semantic_duplicates(lines)

    # 生成报告
    report = []
    report.append("# 重复内容检测报告")
    report.append(f"## 文件: {file_path}")
    report.append(f"## 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report.append(f"## 总行数: {len(lines)}")
    report.append("")

    # 统计信息
    total_issues = len(exact_duplicates) + len(similar_lines) + len(paragraph_duplicates) + len(phrase_duplicates) + len(semantic_duplicates)
    report.append(f"## 检测结果概览")
    report.append(f"- 发现重复问题总数: {total_issues}")
    report.append(f"- 完全重复行: {len(exact_duplicates)}")
    report.append(f"- 语义重复行组: {len(similar_lines)}")
    report.append(f"- 重复段落组: {len(paragraph_duplicates)}")
    report.append(f"- 重复短语: {len(phrase_duplicates)}")
    report.append(f"- 语义重复内容: {len(semantic_duplicates)}")
    report.append("")
    
    # 详细报告
    if exact_duplicates:
        report.append("## 1. 完全重复的行")
        report.append("")
        for i, dup in enumerate(exact_duplicates, 1):
            report.append(f"### 重复组 {i}")
            report.append(f"**重复行号**: {', '.join(map(str, dup['lines']))}")
            report.append(f"**重复内容**: {dup['content']}")
            report.append("")
    
    if similar_lines:
        report.append("## 2. 语义重复的行")
        report.append("")
        for i, sim in enumerate(similar_lines, 1):
            report.append(f"### 相似组 {i}")
            report.append(f"**相似度**: {sim['similarity']:.2%}")
            for line_num, content in sim['lines']:
                report.append(f"- 第{line_num}行: {content}")
            report.append("")
    
    if paragraph_duplicates:
        report.append("## 3. 重复的段落")
        report.append("")
        for i, para_dup in enumerate(paragraph_duplicates, 1):
            report.append(f"### 段落重复组 {i}")
            report.append(f"**相似度**: {para_dup['similarity']:.2%}")
            for para in para_dup['paragraphs']:
                report.append(f"**段落位置**: 第{para['start_line']}-{para['end_line']}行")
                report.append(f"```")
                report.append(para['content'])
                report.append(f"```")
                report.append("")
    
    if phrase_duplicates:
        report.append("## 4. 重复的短语")
        report.append("")
        for i, phrase in enumerate(phrase_duplicates, 1):
            report.append(f"### 短语重复 {i}")
            report.append(f"**出现次数**: {phrase['count']}")
            lines = [str(occ['line']) for occ in phrase['occurrences']]
            report.append(f"**出现行号**: {', '.join(lines)}")
            report.append(f"**重复内容**: {phrase['content']}")
            report.append("**具体位置**:")
            for occ in phrase['occurrences']:
                report.append(f"- 第{occ['line']}行: {occ['context']}")
            report.append("")

    if semantic_duplicates:
        report.append("## 5. 语义重复内容")
        report.append("")
        for i, sem_dup in enumerate(semantic_duplicates, 1):
            report.append(f"### 语义重复组 {i}")
            report.append(f"**相似度**: {sem_dup['similarity']:.2%}")
            report.append("**重复内容**:")
            for sent in sem_dup['sentences']:
                report.append(f"- 第{sent['line']}行: {sent['text']}")
            report.append("")
    
    # 建议
    report.append("## 6. 修改建议")
    report.append("")
    if total_issues > 0:
        report.append("根据检测结果，建议进行以下修改：")
        report.append("")
        if exact_duplicates:
            report.append("1. **完全重复行**: 删除重复的行，保留其中一行即可")
        if similar_lines:
            report.append("2. **语义重复行**: 合并相似内容，避免表达重复")
        if paragraph_duplicates:
            report.append("3. **重复段落**: 重新组织段落结构，避免内容重复")
        if phrase_duplicates:
            report.append("4. **重复短语**: 使用同义词或重新表述，增加文本多样性")
        if semantic_duplicates:
            report.append("5. **语义重复内容**: 重新组织表达方式，避免意思重复")
        report.append("")
        report.append("**注意**: 这是一封情书，某些重复可能是为了强调情感，请根据实际情况判断是否需要修改。")
    else:
        report.append("恭喜！未发现明显的重复内容。")
    
    # 写入报告文件
    try:
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report))
        print(f"报告已生成: {output_path}")
    except Exception as e:
        print(f"写入报告失败: {e}")

if __name__ == "__main__":
    input_file = "outcome/合并后的文件.txt"
    output_file = "重复内容检测报告.md"
    
    generate_report(input_file, output_file)
