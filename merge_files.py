#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件合并脚本
将outcome文件夹中的文件按命名顺序合并成一个文件
"""

import os
import re

def natural_sort_key(filename):
    """
    自然排序的键函数，用于正确排序包含数字的文件名
    例如：1-3.txt, 4-6.txt, 7-10.txt
    """
    # 提取文件名中的数字部分进行排序
    numbers = re.findall(r'\d+', filename)
    return [int(num) for num in numbers]

def merge_files_in_order(input_dir, output_file):
    """
    按文件名顺序合并文件夹中的所有文本文件
    
    Args:
        input_dir (str): 输入文件夹路径
        output_file (str): 输出文件路径
    """
    
    # 检查输入文件夹是否存在
    if not os.path.exists(input_dir):
        print(f"错误：文件夹 {input_dir} 不存在！")
        return
    
    # 获取文件夹中的所有.txt文件
    txt_files = []
    for filename in os.listdir(input_dir):
        if filename.endswith('.txt'):
            txt_files.append(filename)
    
    if not txt_files:
        print(f"错误：文件夹 {input_dir} 中没有找到.txt文件！")
        return
    
    # 按自然顺序排序文件名
    txt_files.sort(key=natural_sort_key)
    
    print(f"开始合并文件夹：{input_dir}")
    print(f"找到 {len(txt_files)} 个文件")
    print(f"文件顺序：{', '.join(txt_files)}")
    print("-" * 50)
    
    try:
        with open(output_file, 'w', encoding='utf-8') as output:
            total_lines = 0
            
            for i, filename in enumerate(txt_files, 1):
                file_path = os.path.join(input_dir, filename)
                
                print(f"正在处理：{filename}")
                
                with open(file_path, 'r', encoding='utf-8') as input_file:
                    lines = input_file.readlines()
                    
                    # 写入文件内容
                    for line in lines:
                        output.write(line)
                    
                    # 如果不是最后一个文件，并且文件末尾没有换行符，则添加换行符
                    if i < len(txt_files) and lines and not lines[-1].endswith('\n'):
                        output.write('\n')
                    
                    file_lines = len(lines)
                    total_lines += file_lines
                    print(f"  - 已添加 {file_lines} 行")
        
        print("-" * 50)
        print(f"合并完成！")
        print(f"输出文件：{output_file}")
        print(f"总共合并了 {len(txt_files)} 个文件")
        print(f"总行数：{total_lines} 行")
        
        # 验证合并结果
        print("\n验证合并结果：")
        with open(output_file, 'r', encoding='utf-8') as f:
            merged_lines = len(f.readlines())
        print(f"合并后文件行数：{merged_lines} 行")
        
        if merged_lines == total_lines:
            print("✓ 合并验证成功！")
        else:
            print("⚠ 合并验证警告：行数可能有差异")
            
    except Exception as e:
        print(f"合并文件时发生错误：{e}")

if __name__ == "__main__":
    # 合并outcome文件夹中的文件
    input_directory = "outcome"
    output_filename = "合并后的文件.txt"
    merge_files_in_order(input_directory, output_filename)
